"""
记忆分区管理API路由

处理记忆分区的配置和数据管理，包括：
- 分区策略配置
- 分区列表查询
- 分区数据可视化
- 分区内容搜索
"""

from flask import Blueprint, request, jsonify, current_app
from app.models import GraphEnhancement, db
from app.services.memory_partition_service import memory_partition_service

# 创建Blueprint
memory_management_bp = Blueprint('memory_management_api', __name__)


# ==================== 分区配置管理接口 ====================

@memory_management_bp.route('/memory/partition-config', methods=['GET'])
def get_partition_config():
    """获取记忆分区配置"""
    try:
        config = memory_partition_service.get_partition_config()
        
        return jsonify({
            'success': True,
            'data': config
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分区配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取分区配置失败: {str(e)}'
        }), 500


@memory_management_bp.route('/memory/partition-config', methods=['POST'])
def update_partition_config():
    """更新记忆分区配置"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        if 'partition_strategy' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要字段: partition_strategy'
            }), 400
        
        # 更新配置
        success, message = memory_partition_service.update_partition_config(data)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': memory_partition_service.get_partition_config()
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"更新分区配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新分区配置失败: {str(e)}'
        }), 500


@memory_management_bp.route('/memory/partition-strategies', methods=['GET'])
def get_partition_strategies():
    """获取可用的分区策略列表"""
    try:
        strategies = memory_partition_service.get_available_strategies()
        
        return jsonify({
            'success': True,
            'data': strategies
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分区策略失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取分区策略失败: {str(e)}'
        }), 500


# ==================== 分区数据管理接口 ====================

@memory_management_bp.route('/memory/partitions', methods=['GET'])
def list_memory_partitions():
    """获取所有可用的记忆分区列表"""
    try:
        partitions = memory_partition_service.list_partitions()
        
        return jsonify({
            'success': True,
            'data': partitions
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分区列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取分区列表失败: {str(e)}'
        }), 500


@memory_management_bp.route('/memory/partition/<partition_id>/graph', methods=['GET'])
def get_partition_memory_graph(partition_id):
    """获取指定分区的记忆图谱数据"""
    try:
        # 获取查询参数
        limit = request.args.get('limit', 100, type=int)
        node_types = request.args.getlist('node_types')
        
        graph_data = memory_partition_service.get_partition_graph(
            partition_id, 
            limit=limit,
            node_types=node_types
        )
        
        return jsonify({
            'success': True,
            'data': graph_data
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分区图谱数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取分区图谱数据失败: {str(e)}'
        }), 500


@memory_management_bp.route('/memory/partition/<partition_id>/search', methods=['POST'])
def search_partition_memory(partition_id):
    """在指定分区中搜索记忆内容"""
    try:
        data = request.get_json()
        
        query = data.get('query', '')
        if not query:
            return jsonify({
                'success': False,
                'message': '搜索查询不能为空'
            }), 400
        
        # 搜索参数
        search_params = {
            'limit': data.get('limit', 10),
            'node_types': data.get('node_types', []),
            'search_mode': data.get('search_mode', 'semantic')
        }
        
        results = memory_partition_service.search_partition(
            partition_id, 
            query, 
            **search_params
        )
        
        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        current_app.logger.error(f"搜索分区记忆失败: {e}")
        return jsonify({
            'success': False,
            'message': f'搜索分区记忆失败: {str(e)}'
        }), 500


# ==================== 分区统计接口 ====================

@memory_management_bp.route('/memory/partition/<partition_id>/stats', methods=['GET'])
def get_partition_stats(partition_id):
    """获取分区统计信息"""
    try:
        stats = memory_partition_service.get_partition_stats(partition_id)
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分区统计失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取分区统计失败: {str(e)}'
        }), 500


@memory_management_bp.route('/memory/overview', methods=['GET'])
def get_memory_overview():
    """获取记忆系统总览"""
    try:
        overview = memory_partition_service.get_memory_overview()
        
        return jsonify({
            'success': True,
            'data': overview
        })
        
    except Exception as e:
        current_app.logger.error(f"获取记忆系统总览失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取记忆系统总览失败: {str(e)}'
        }), 500


# ==================== 分区管理接口 ====================

@memory_management_bp.route('/memory/partition/<partition_id>/clear', methods=['POST'])
def clear_partition_memory(partition_id):
    """清空指定分区的记忆数据"""
    try:
        success, message = memory_partition_service.clear_partition(partition_id)
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        current_app.logger.error(f"清空分区记忆失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空分区记忆失败: {str(e)}'
        }), 500
