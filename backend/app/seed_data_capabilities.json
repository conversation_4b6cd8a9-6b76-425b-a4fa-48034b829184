[{"name": "tool_use", "description": "允许智能体使用外部工具，符合MCP标准的工具调用能力，适用于Claude、OpenAI等模型", "type": "core", "provider": "default", "parameters": {"tool_name": {"type": "string", "description": "要使用的工具名称"}, "parameters": {"type": "object", "description": "工具调用参数"}}, "response_format": {"result": {"type": "any", "description": "工具执行结果"}, "success": {"type": "boolean", "description": "工具执行是否成功"}}, "examples": [{"input": {"tool_name": "search_web", "parameters": {"query": "最新人工智能发展趋势"}}, "output": {"success": true, "result": "搜索结果：1. 生成式AI继续发展；2. 多模态模型成为主流；3. AI在特定领域应用深化..."}}], "settings": {"strict_schema": true, "allow_multiple_tools": true, "mcp_compatible": true}}, {"name": "function_calling", "description": "允许智能体使用函数调用功能，符合OpenAI函数调用标准，支持结构化输出和API调用", "type": "core", "provider": "default", "parameters": {"function_name": {"type": "string", "description": "要调用的函数名称"}, "arguments": {"type": "object", "description": "函数调用参数"}}, "response_format": {"result": {"type": "any", "description": "函数执行结果"}, "success": {"type": "boolean", "description": "函数执行是否成功"}}, "examples": [{"input": {"function_name": "get_weather", "arguments": {"location": "北京", "unit": "celsius"}}, "output": {"success": true, "result": {"temperature": 25, "conditions": "晴朗", "humidity": 40}}}], "settings": {"json_mode": true, "parallel_calling": false, "openai_compatible": true}}, {"name": "environment_sensing", "description": "允许智能体感知公共环境变量和角色专属变量，作为决策的基础", "type": "core", "provider": "default", "parameters": {"variable_types": {"type": "array", "description": "要感知的变量类型", "enum": ["public", "role", "all"]}}, "response_format": {"variables": {"type": "object", "description": "感知到的环境变量"}}, "examples": [{"input": {"variable_types": ["public"]}, "output": {"variables": {"project_deadline": "2023-12-31", "budget_remaining": 50000}}}], "settings": {"refresh_rate": "per_round"}}, {"name": "environment_modification", "description": "允许智能体修改公共环境变量和角色专属变量，改变环境状态", "type": "core", "provider": "default", "parameters": {"variable_types": {"type": "array", "description": "要修改的变量类型", "enum": ["public", "role", "all"]}, "variable_name": {"type": "string", "description": "要修改的变量名称"}, "variable_value": {"type": "any", "description": "变量的新值"}}, "response_format": {"success": {"type": "boolean", "description": "修改是否成功"}, "previous_value": {"type": "any", "description": "变量的先前值"}}, "examples": [{"input": {"variable_types": ["public"], "variable_name": "project_deadline", "variable_value": "2024-01-15"}, "output": {"success": true, "previous_value": "2023-12-31"}}], "settings": {"audit_trail": true, "confirmation_required": true}}, {"name": "workspace_management", "description": "智能体项目空间管理能力，包括个人工作空间和共享工作区的文件存储与检索", "type": "core", "provider": "default", "parameters": {"workspace_type": {"type": "string", "description": "工作空间类型", "enum": ["personal", "shared", "both"]}, "content": {"type": "string", "description": "要存储的项目文件内容"}}, "response_format": {"success": {"type": "boolean", "description": "存储是否成功"}, "file_id": {"type": "string", "description": "项目文件的唯一标识"}}, "examples": [{"input": {"workspace_type": "personal", "content": "客户强调产品安全性是首要考虑因素"}, "output": {"success": true, "file_id": "workspace-12345"}}], "settings": {"retention_policy": "importance_based"}}, {"name": "explicit_reasoning", "description": "在回应前进行显性推理过程，展示详细的思考步骤和决策依据", "type": "advanced", "provider": "default", "parameters": {"depth": {"type": "string", "description": "推理深度", "enum": ["basic", "intermediate", "advanced"]}, "question": {"type": "string", "description": "需要推理的问题"}}, "response_format": {"reasoning_steps": {"type": "array", "description": "推理步骤"}, "conclusion": {"type": "string", "description": "推理结论"}}, "examples": [{"input": {"depth": "advanced", "question": "是否应该升级数据库架构?"}, "output": {"reasoning_steps": ["1. 考虑当前性能问题", "2. 分析系统负载预测", "3. 评估升级成本和风险", "4. 比较不同方案的投资回报"], "conclusion": "基于性能瓶颈和未来扩展需求，建议升级到分布式数据库架构"}}], "settings": {"show_alternatives": true}}, {"name": "team_coordination", "description": "协调多个智能体的工作，分配任务并监督协作过程", "type": "advanced", "provider": "default", "parameters": {"task": {"type": "string", "description": "需要协调的任务"}, "participants": {"type": "array", "description": "参与者列表"}}, "response_format": {"assignments": {"type": "object", "description": "任务分配"}, "timeline": {"type": "array", "description": "协作时间线"}}, "examples": [{"input": {"task": "开发新的用户注册流程", "participants": ["产品经理", "UI设计师", "后端开发", "测试工程师"]}, "output": {"assignments": {"产品经理": "定义需求", "UI设计师": "设计界面", "后端开发": "实现API", "测试工程师": "编写测试用例"}, "timeline": [{"phase": "需求分析", "duration": "3天"}, {"phase": "设计", "duration": "4天"}, {"phase": "开发", "duration": "7天"}, {"phase": "测试", "duration": "4天"}]}}], "settings": {"feedback_loops": true}}, {"name": "behavior_monitoring", "description": "观察和分析其他智能体的行为模式，识别潜在问题和优化机会", "type": "supervision", "provider": "default", "parameters": {"target_agents": {"type": "array", "description": "观察目标"}, "focus_areas": {"type": "array", "description": "关注的行为领域"}}, "response_format": {"observations": {"type": "array", "description": "行为观察结果"}, "patterns": {"type": "array", "description": "识别的模式"}}, "examples": [{"input": {"target_agents": ["all"], "focus_areas": ["decision_making", "collaboration", "information_sharing"]}, "output": {"observations": [{"agent": "产品经理", "behavior": "未充分考虑技术约束"}, {"agent": "开发工程师", "behavior": "过度关注细节实现而非用户需求"}], "patterns": [{"pattern": "需求沟通不足", "frequency": "high", "impact": "导致返工和时间浪费"}]}}], "settings": {"monitoring_frequency": "continuous", "privacy_level": "high"}}, {"name": "intervention", "description": "在必要时干预交互过程，纠正错误决策或提供额外信息", "type": "supervision", "provider": "default", "parameters": {"intervention_type": {"type": "string", "description": "干预类型", "enum": ["information", "correction", "guidance", "redirection"]}, "target_agent": {"type": "string", "description": "干预目标"}, "content": {"type": "string", "description": "干预内容"}}, "response_format": {"success": {"type": "boolean", "description": "干预是否成功"}, "effect": {"type": "string", "description": "干预效果"}}, "examples": [{"input": {"intervention_type": "correction", "target_agent": "开发工程师", "content": "你的数据库设计方案未考虑高并发情况，建议重新评估"}, "output": {"success": true, "effect": "开发工程师接受建议，修改了数据库设计方案"}}], "settings": {"intervention_threshold": "medium", "require_confirmation": true}}, {"name": "free_will", "description": "赋予智能体自由意志能力，允许其创建和定义自己的变量，实现更高级别的自主性", "type": "advanced", "provider": "default", "parameters": {"variable_scope": {"type": "string", "description": "变量作用域", "enum": ["task", "agent", "both"]}, "variable_definition": {"type": "object", "description": "变量定义", "properties": {"name": {"type": "string", "description": "变量名称"}, "type": {"type": "string", "description": "变量类型（固定为text）", "enum": ["text"]}, "value": {"type": "string", "description": "变量值（文本类型）"}, "label": {"type": "string", "description": "变量显示标签"}}}}, "response_format": {"success": {"type": "boolean", "description": "创建是否成功"}, "variable_id": {"type": "string", "description": "创建的变量ID"}}, "examples": [{"input": {"variable_scope": "agent", "variable_definition": {"name": "creativity_level", "type": "number", "value": 85, "label": "创造力水平", "unit": "%"}}, "output": {"success": true, "variable_id": "var-agent-123-creativity_level"}}], "settings": {"security_level": 3, "default_enabled": false, "audit_trail": true, "confirmation_required": true}}, {"name": "knowledge_access", "description": "允许智能体查询和访问绑定的知识库与记忆，获取相关信息以辅助决策和回答", "type": "core", "provider": "default", "parameters": {"query": {"type": "string", "description": "查询的问题或关键词"}, "knowledge_types": {"type": "array", "description": "要查询的知识库类型", "enum": ["internal", "external", "memory", "all"], "default": ["all"]}, "max_results": {"type": "integer", "description": "最大返回结果数量", "default": 5, "minimum": 1, "maximum": 20}, "similarity_threshold": {"type": "number", "description": "相似度阈值", "default": 0.7, "minimum": 0.0, "maximum": 1.0}}, "response_format": {"results": {"type": "array", "description": "查询结果列表", "items": {"type": "object", "properties": {"source": {"type": "string", "description": "知识来源"}, "content": {"type": "string", "description": "相关内容"}, "similarity": {"type": "number", "description": "相似度分数"}, "metadata": {"type": "object", "description": "元数据信息"}}}}, "total_found": {"type": "integer", "description": "找到的总结果数"}, "query_time": {"type": "number", "description": "查询耗时（毫秒）"}}, "examples": [{"input": {"query": "如何优化数据库性能", "knowledge_types": ["internal", "external"], "max_results": 3}, "output": {"results": [{"source": "内部知识库-数据库优化指南", "content": "数据库性能优化的关键策略包括：1. 索引优化 2. 查询优化 3. 缓存策略...", "similarity": 0.92, "metadata": {"document": "db_optimization_guide.pdf", "section": "性能调优"}}, {"source": "外部知识库-技术文档", "content": "MySQL性能调优最佳实践：使用EXPLAIN分析查询计划，合理设计索引结构...", "similarity": 0.87, "metadata": {"provider": "技术文档库", "last_updated": "2024-01-15"}}], "total_found": 8, "query_time": 245.6}}, {"input": {"query": "项目进度", "knowledge_types": ["memory"], "max_results": 2}, "output": {"results": [{"source": "对话记忆", "content": "上次讨论中提到项目当前进度为75%，预计下周完成前端开发", "similarity": 0.89, "metadata": {"conversation_id": "conv_123", "timestamp": "2024-01-20T10:30:00Z"}}], "total_found": 3, "query_time": 156.2}}], "settings": {"security_level": 1, "default_enabled": true, "cache_results": true, "cache_duration": 300, "enable_semantic_search": true, "enable_hybrid_search": true}}, {"name": "memory", "description": "## Instructions for Using <PERSON><PERSON><PERSON><PERSON>'s MCP Tools for Agent Memory\n\n### Before Starting Any Task\n\n- **Always search first:** Use the `search_nodes` tool to look for relevant preferences and procedures before beginning work.\n- **Search for facts too:** Use the `search_facts` tool to discover relationships and factual information that may be relevant to your task.\n- **Filter by entity type:** Specify `Preference`, `Procedure`, or `Requirement` in your node search to get targeted results.\n- **Review all matches:** Carefully examine any preferences, procedures, or facts that match your current task.\n\n### Always Save New or Updated Information\n\n- **Capture requirements and preferences immediately:** When a user expresses a requirement or preference, use `add_memory` to store it right away.\n  - _Best practice:_ Split very long requirements into shorter, logical chunks.\n- **Be explicit if something is an update to existing knowledge.** Only add what's changed or new to the graph.\n- **Document procedures clearly:** When you discover how a user wants things done, record it as a procedure.\n- **Record factual relationships:** When you learn about connections between entities, store these as facts.\n- **Be specific with categories:** Label preferences and procedures with clear categories for better retrieval later.\n\n### During Your Work\n\n- **Respect discovered preferences:** Align your work with any preferences you've found.\n- **Follow procedures exactly:** If you find a procedure for your current task, follow it step by step.\n- **Apply relevant facts:** Use factual information to inform your decisions and recommendations.\n- **Stay consistent:** Maintain consistency with previously identified preferences, procedures, and facts.\n\n### Best Practices\n\n- **Search before suggesting:** Always check if there's established knowledge before making recommendations.\n- **Combine node and fact searches:** For complex tasks, search both nodes and facts to build a complete picture.\n- **Use `center_node_uuid`:** When exploring related information, center your search around a specific node.\n- **Prioritize specific matches:** More specific information takes precedence over general information.\n- **Be proactive:** If you notice patterns in user behavior, consider storing them as preferences or procedures.\n\n**Remember:** The knowledge graph is your memory. Use it consistently to provide personalized assistance that respects the user's established preferences, procedures, and factual context.", "type": "mcp_integration", "provider": "default", "parameters": {"query": {"type": "string", "description": "搜索查询或要存储的内容"}, "operation": {"type": "string", "description": "操作类型", "enum": ["search", "add", "update", "delete"]}}, "response_format": {"success": {"type": "boolean", "description": "操作是否成功"}, "results": {"type": "array", "description": "搜索结果或操作结果"}}, "examples": [{"input": {"operation": "search", "query": "用户偏好"}, "output": {"success": true, "results": [{"type": "Preference", "content": "用户偏好使用简洁的界面设计", "uuid": "pref-123"}]}}, {"input": {"operation": "add", "query": "用户要求所有报告必须包含数据来源"}, "output": {"success": true, "results": [{"type": "Requirement", "uuid": "req-456", "message": "已保存新的需求"}]}}], "settings": {"security_level": 1, "default_enabled": false, "mcp_servers": ["graphiti-server"], "auto_partition": true, "partition_strategy": "by_space"}}]