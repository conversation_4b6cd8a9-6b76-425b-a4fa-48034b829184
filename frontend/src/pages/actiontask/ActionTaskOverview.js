import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '@ant-design/v5-patch-for-react-19';
import {
  Table,
  Button,
  Card,
  Tag,
  Space,
  Badge,
  Tooltip,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Divider,
  Row,
  Col,
  Empty,
  Tabs,
  Radio,
  Spin,
  App
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  MessageOutlined,
  GlobalOutlined,
  OrderedListOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeOutlined,

  StopOutlined,
  FileProtectOutlined,
  AppstoreOutlined,
  ExclamationCircleOutlined,
  CommentOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { actionTaskAPI } from '../../services/api/actionTask';
import { agentAPI } from '../../services/api/agent';
import { roleAPI } from '../../services/api/role';
import { actionSpaceAPI } from '../../services/api/actionspace';
import { modelConfigAPI } from '../../services/api/model';
import { settingsAPI } from '../../services/api/settings';
import { replaceTemplateVariables, formatRolesForTemplate } from '../../utils/templateUtils';
import { getAssistantGenerationModelId } from '../../utils/modelUtils';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
// 不再使用 TabPane，改用 items 属性

const ActionTaskOverview = () => {
  // 使用 App 上下文中的 message
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [createLoading, setCreateLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [agents, setAgents] = useState([]);
  const [actionSpaces, setActionSpaces] = useState([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [selectedActionSpace, setSelectedActionSpace] = useState(null);
  const [ruleSets, setRuleSets] = useState([]);
  const [loadingRuleSets, setLoadingRuleSets] = useState(false);
  const [viewMode, setViewMode] = useState('card');
  const [selectedTasks, setSelectedTasks] = useState([]);

  // 辅助生成相关状态
  const [assistantGenerating, setAssistantGenerating] = useState(false);
  const [globalSettings, setGlobalSettings] = useState({
    enableAssistantGeneration: true,
    assistantGenerationModel: 'default'
  });
  const [modelConfigs, setModelConfigs] = useState([]);

  // 获取全局设置
  const fetchGlobalSettings = async () => {
    try {
      const settings = await settingsAPI.getSettings();
      setGlobalSettings({
        enableAssistantGeneration: settings.enableAssistantGeneration !== undefined ? settings.enableAssistantGeneration : true,
        assistantGenerationModel: settings.assistantGenerationModel || 'default'
      });
    } catch (error) {
      console.error('获取全局设置失败:', error);
    }
  };

  // 获取模型配置
  const fetchModelConfigs = async () => {
    try {
      const configs = await modelConfigAPI.getAll();
      setModelConfigs(configs);
    } catch (error) {
      console.error('获取模型配置失败:', error);
    }
  };

  // 移除筛选菜单定义

  useEffect(() => {
    // 初始加载时只获取一次数据
    const initialDataFetch = async () => {
      setLoading(true); // 设置全局加载状态

      try {
        // 获取行动空间列表
        let allActionSpaces = [];
        try {
          const actionSpacesResponse = await actionSpaceAPI.getAll();
          if (actionSpacesResponse && Array.isArray(actionSpacesResponse)) {
            allActionSpaces = actionSpacesResponse;
            setActionSpaces(allActionSpaces);
            console.log('获取行动空间列表成功', allActionSpaces.length, '个行动空间');

            // 从行动空间中提取规则集
            try {
              const extractedRuleSets = await actionSpaceAPI.getRuleSets(null, allActionSpaces);
              setRuleSets(extractedRuleSets);
            } catch (ruleSetsError) {
              console.error('提取规则集失败:', ruleSetsError);
            }
          }
        } catch (spaceError) {
          console.error('获取行动空间列表失败:', spaceError);
        }

        // 获取任务数据
        let apiTasks = [];
        try {
          const response = await actionTaskAPI.getAll();
          if (response && Array.isArray(response) && response.length > 0) {
            apiTasks = response.map(task => {
              // 查找对应的行动空间名称
              let actionSpaceName = task.action_space_name;
              if (!actionSpaceName && task.action_space_id) {
                const matchedSpace = allActionSpaces.find(space => space.id === task.action_space_id);
                actionSpaceName = matchedSpace?.name || '未知行动空间';
              } else if (!actionSpaceName) {
                actionSpaceName = '未分配行动空间';
              }
              return {
                ...task,
                action_space_name: actionSpaceName,
                is_api: true
              };
            });
            console.log('成功加载API行动任务数据', apiTasks.length, '条记录');
          }
        } catch (apiError) {
          console.error('获取API行动任务失败:', apiError);
          message.warning('获取后端任务数据失败');
        }

        // 设置任务数据
        setTasks(apiTasks);

        if (apiTasks.length > 0) {
          message.success(`已加载${apiTasks.length}个任务`);
        } else {
          message.info('暂无任务数据');
        }
      } catch (error) {
        console.error('数据加载失败:', error);
        message.error('数据加载失败: ' + error.message);
        setTasks([]);
      } finally {
        setLoading(false);
      }
    };

    initialDataFetch();
    fetchGlobalSettings();
    fetchModelConfigs();
  }, []);

  // 刷新任务列表（如果需要重新加载数据）
  const refreshTasks = async () => {
    // 实现刷新逻辑，如果需要的话
    message.info('刷新任务列表功能尚未实现');
  };

  // 加载智能体和行动空间数据
  const loadResources = async () => {
    setLoadingResources(true);
    try {
      // 获取所有角色列表
      const rolesData = await roleAPI.getAll();
      setAgents(rolesData);

      // 获取行动空间列表
      const actionSpacesData = await actionSpaceAPI.getAll();
      setActionSpaces(actionSpacesData);
    } catch (error) {
      message.error('加载资源失败，请稍后重试');
      console.error('加载资源失败:', error);
    } finally {
      setLoadingResources(false);
    }
  };

  // 打开创建任务的模态框
  const showCreateModal = () => {
    setModalVisible(true);
    // 重置表单
    form.resetFields();
    // 加载必要资源
    loadResources();
  };

  // 创建新任务
  const handleCreateTask = async (values) => {
    setCreateLoading(true);
    try {
      // 不再需要手动创建智能体，后端会自动从行动空间角色创建参与智能体，从监督者角色创建监督者agent
      // 这里不再需要从角色创建智能体的代码，后端会自动处理

      // 保留一个空的agentIds数组，以便与旧版本兼容
      const agentIds = [];

      // 获取选择的规则集ID（可能是多个）
      const ruleSetIds = values.rule_set_id || [];

      // 使用第一个规则集作为主规则集，或者如果没有选择规则集则为空
      const primaryRuleSetId = ruleSetIds.length > 0 ? ruleSetIds[0] : null;

      // 构建请求数据
      const taskData = {
        title: values.title,
        description: values.description || '',
        mode: 'sequential', // 默认使用顺序模式
        action_space_id: values.action_space_id,
        rule_set_id: primaryRuleSetId, // 主规则集
        additional_rule_set_ids: ruleSetIds.slice(1), // 额外的规则集
        agent_ids: agentIds  // 添加智能体ID列表
      };

      console.log('创建行动任务数据:', taskData);

      // 调用API创建任务
      const response = await actionTaskAPI.create(taskData);

      // 添加到列表中
      if (response && response.id) {
        message.success('行动任务创建成功');
        setModalVisible(false);

        // 查找行动空间名称
        const actionSpace = actionSpaces.find(space => space.id === values.action_space_id);
        const actionSpaceName = actionSpace ? actionSpace.name : '未知行动空间';

        // 构建新任务对象，确保与API返回格式一致
        const newTask = {
          id: response.id,
          title: response.title,
          description: values.description || '',
          status: 'active',
          mode: 'sequential', // 默认使用顺序模式
          rule_set_id: primaryRuleSetId,
          action_space_id: values.action_space_id,
          action_space_name: actionSpaceName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          agent_count: response.agent_count || agentIds.length,
          message_count: response.message_count || 0,
          conversation_count: response.conversation_count || 0,
          autonomous_task_count: response.autonomous_task_count || 0,
          supervisor_count: 1,
          // 使用API返回的环境变量或从任务详情API获取
          environment_variables: response.environment_variables || [],
          rule_triggers: [],
          is_api: true,  // 标记为API数据
          agent_ids: agentIds  // 添加智能体ID列表
        };

        // 将新任务添加到列表开头
        setTasks(prev => [newTask, ...prev]);

        console.log('添加新创建的任务到列表:', newTask);

        // 跳转到任务详情页
        setTimeout(() => {
          navigate(`/action-tasks/detail/${response.id}`);
        }, 500);
      }
    } catch (error) {
      message.error(`创建行动任务失败: ${error.message || '请稍后重试'}`);
      console.error('创建行动任务失败:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 过滤任务数据
  const getFilteredTasks = () => {
    // 先根据搜索文本过滤任务
    let filteredTasks = tasks;
    if (searchText) {
      filteredTasks = tasks.filter(task =>
        task.title.toLowerCase().includes(searchText.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchText.toLowerCase())) ||
        (task.action_space_name && task.action_space_name.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 按创建时间降序排序，最新的排在前面
    return filteredTasks.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  };

  // 辅助生成任务描述
  const handleAssistantGenerate = async () => {
    try {
      // 检查是否启用了辅助生成
      if (!globalSettings.enableAssistantGeneration) {
        message.warning('辅助生成功能未启用，请在系统设置中开启');
        return;
      }

      // 获取当前表单的任务名称和行动空间
      const values = form.getFieldsValue(['title', 'action_space_id']);

      if (!values.title) {
        message.warning('请先填写任务名称，然后再使用辅助生成');
        return;
      }

      if (!values.action_space_id) {
        message.warning('请先选择行动空间，然后再使用辅助生成');
        return;
      }

      setAssistantGenerating(true);

      // 获取行动空间详细信息
      const actionSpace = actionSpaces.find(space => space.id === values.action_space_id);
      if (!actionSpace) {
        message.error('未找到选择的行动空间信息');
        return;
      }

      // 获取行动空间内的角色信息
      let roles = [];
      if (selectedActionSpace && selectedActionSpace.roles && selectedActionSpace.roles.length > 0) {
        roles = selectedActionSpace.roles;
      }

      // 获取系统设置的提示词模板
      let promptTemplate;
      try {
        const templates = await settingsAPI.getPromptTemplates();
        promptTemplate = templates.actionTaskDescription;
        if (!promptTemplate) {
          throw new Error('未获取到任务描述生成模板');
        }
      } catch (error) {
        console.error('获取提示词模板失败:', error);
        message.error('获取提示词模板失败，请检查系统设置');
        setAssistantGenerating(false);
        return;
      }

      // 使用模板变量替换功能
      const generatePrompt = replaceTemplateVariables(promptTemplate, {
        title: values.title,
        action_space_name: actionSpace.name,
        action_space_description: actionSpace.description || '无描述',
        roles: formatRolesForTemplate(roles)
      });

      // 确定使用的模型
      const modelToUse = await getAssistantGenerationModelId(modelConfigs, globalSettings.assistantGenerationModel);

      // 调用模型API生成描述
      let generatedDescription = '';
      const handleStreamResponse = (chunk) => {
        // 过滤掉null、undefined和空字符串
        if (chunk && chunk !== 'null' && chunk !== 'undefined' && typeof chunk === 'string') {
          generatedDescription += chunk;
          // 实时更新表单中的任务描述字段
          form.setFieldsValue({
            description: generatedDescription
          });
        }
      };

      await modelConfigAPI.testModelStream(
        modelToUse,
        generatePrompt,
        handleStreamResponse,
        "你是一个专业的任务规划专家，擅长根据行动空间信息和任务名称生成详细的任务描述。",
        {
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      // 最终清理生成的内容，移除可能的null字符串
      const cleanedDescription = generatedDescription
        .replace(/null/g, '')
        .replace(/undefined/g, '')
        .trim();

      form.setFieldsValue({
        description: cleanedDescription
      });

      message.success('任务描述生成完成');
    } catch (error) {
      console.error('辅助生成失败:', error);
      message.error(`辅助生成失败: ${error.message || '未知错误'}`);
    } finally {
      setAssistantGenerating(false);
    }
  };

  // 处理行动空间变更
  const handleActionSpaceChange = async (spaceId) => {
    if (!spaceId) {
      setSelectedActionSpace(null);
      // 清空规则集选择
      form.setFieldsValue({ rule_set_id: [] });
      return;
    }

    try {
      // 获取行动空间详情
      const spaceDetail = await actionSpaceAPI.getDetail(spaceId);
      setSelectedActionSpace(spaceDetail);

      // 筛选该行动空间的规则集
      const spaceRuleSets = ruleSets.filter(rs => rs.action_space_id === spaceId);

      // 如果找到了规则集，自动选中它们
      if (spaceRuleSets.length > 0) {
        const ruleSetIds = spaceRuleSets.map(rs => rs.id);
        form.setFieldsValue({ rule_set_id: ruleSetIds });
        console.log(`自动选择行动空间 ${spaceId} 的 ${ruleSetIds.length} 个规则集`);
      } else {
        // 如果没有找到规则集，清空选择
        form.setFieldsValue({ rule_set_id: [] });
        console.log(`行动空间 ${spaceId} 没有关联的规则集`);
      }
    } catch (error) {
      console.error('获取行动空间详情失败:', error);
      setSelectedActionSpace({
        id: spaceId,
        environment_variables: [],
        roles: []
      });
      message.error('获取行动空间详情失败，请稍后重试');
    }
  };

  // 渲染任务卡片
  const renderTaskCards = (filteredTasks = getFilteredTasks()) => {
    return (
      <Row gutter={[16, 16]}>
        {filteredTasks.map(task => (
          <Col xs={24} sm={12} md={8} lg={6} key={task.id}>
            <Card
              size="small"
              hoverable
              className="task-card"
              style={{
                height: '100%',
                borderRadius: '8px',
                border: '1px solid #d9d9d9',
                display: 'flex',
                flexDirection: 'column'
              }}
              styles={{
                body: {
                  padding: '12px',
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column'
                }
              }}
              actions={[
                <Tooltip title="查看详情">
                  <EyeOutlined key="view" onClick={() => navigate(`/action-tasks/detail/${task.id}`)} />
                </Tooltip>,
                <Tooltip title="归档任务">
                  <StopOutlined key="stop" onClick={() => handleTerminateTask(task.id)} />
                </Tooltip>,
                <Tooltip title="删除任务">
                  <DeleteOutlined key="delete" style={{ color: '#ff4d4f' }} onClick={(e) => handleDeleteTask(task.id, e)} />
                </Tooltip>,
              ]}
            >
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column', cursor: 'pointer' }} onClick={() => navigate(`/action-tasks/detail/${task.id}`)}>
                {/* 标题和状态水平对齐 */}
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 10 }}>
                  <Title level={5} ellipsis={{ rows: 2 }} style={{ marginTop: 0, marginBottom: 0, flex: 1, marginRight: 8 }}>
                    {task.title}
                  </Title>
                  <div style={{ flexShrink: 0 }}>
                    {task.status === 'active' && (
                      <Badge status="processing" text="进行中" />
                    )}

                    {task.status === 'completed' && (
                      <Badge status="success" text="已完成" />
                    )}
                    {task.status === 'terminated' && (
                      <Badge status="error" text="已终止" />
                    )}
                  </div>
                </div>
                <Paragraph type="secondary" ellipsis={{ rows: 2 }}>
                  {task.description || '无描述'}
                </Paragraph>

                {/* 关键信息区域 - 使用 marginTop: 'auto' 推到底部 */}
                <div className="task-info-section" style={{ marginTop: 'auto' }}>
                  <Divider />
                  <Space direction="vertical" size="small" style={{ width: '100%' }} className="info-content">
                    <div>
                      <GlobalOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                      <Text type="secondary">行动空间：</Text>
                      <Text strong style={{ color: '#1890ff' }}>{task.action_space_name || '未指定'}</Text>
                    </div>
                    <div>
                      <TeamOutlined style={{ marginRight: 8 }} />
                      <Text type="secondary">智能体：</Text>
                      <Text>{task.agent_count || 0}个</Text>
                    </div>
                    <div>
                      <CommentOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                      <Text type="secondary">会话：</Text>
                      <Text>{task.conversation_count || 0}个</Text>
                    </div>
                    <div>
                      <MessageOutlined style={{ marginRight: 8 }} />
                      <Text type="secondary">消息：</Text>
                      <Text>{task.message_count || 0}条</Text>
                    </div>
                    <div>
                      <RobotOutlined style={{ marginRight: 8, color: '#722ed1' }} />
                      <Text type="secondary">自主行动：</Text>
                      <Text>
                        活动{task.active_autonomous_task_count || 0}个/共{task.total_autonomous_task_count || task.autonomous_task_count || 0}个
                      </Text>
                    </div>
                    <div>
                      <ClockCircleOutlined style={{ marginRight: 8 }} />
                      <Text type="secondary">创建于：</Text>
                      <Text>{new Date(task.created_at).toLocaleString()}</Text>
                    </div>
                    <div>
                      <ClockCircleOutlined style={{ marginRight: 8, color: '#faad14' }} />
                      <Text type="secondary">更新于：</Text>
                      <Text>{new Date(task.updated_at).toLocaleString()}</Text>
                    </div>
                  </Space>
                </div>
              </div>
            </Card>
          </Col>
        ))}

        {/* 添加新任务卡片 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            hoverable
            className="add-task-card"
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '300px',
              border: '2px dashed #d9d9d9',
              backgroundColor: '#fafafa',
              borderRadius: '8px'
            }}
            onClick={showCreateModal}
          >
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <PlusOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
              <Title level={4} style={{ color: '#1890ff', marginBottom: '8px' }}>
                创建新任务
              </Title>
              <Text type="secondary">
                点击创建新的行动任务
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div style={{ cursor: 'pointer' }} onClick={() => navigate(`/action-tasks/detail/${record.id}`)}>
          <Text strong>{text}</Text>
        </div>
      ),
    },
    {
      title: '行动空间',
      dataIndex: 'action_space_name',
      key: 'action_space_name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: status => {
        if (status === 'active') {
          return <Badge status="processing" text="进行中" />;
        } else if (status === 'completed') {
          return <Badge status="success" text="已完成" />;
        } else if (status === 'terminated') {
          return <Badge status="error" text="已终止" />;
        }
        return <Badge status="default" text="未知状态" />;
      },
    },
    {
      title: '智能体',
      dataIndex: 'agent_count',
      key: 'agent_count',
      render: count => `${count || 0}个`,
    },
    {
      title: '会话数',
      dataIndex: 'conversation_count',
      key: 'conversation_count',
      render: count => `${count || 0}个`,
    },
    {
      title: '消息数',
      dataIndex: 'message_count',
      key: 'message_count',
      render: count => `${count || 0}条`,
    },
    {
      title: '自主行动',
      dataIndex: 'autonomous_task_count',
      key: 'autonomous_task_count',
      render: (_, record) => {
        const activeCount = record.active_autonomous_task_count || 0;
        const totalCount = record.total_autonomous_task_count || record.autonomous_task_count || 0;
        return `活动${activeCount}个/共${totalCount}个`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: time => new Date(time).toLocaleString(),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: time => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => navigate(`/action-tasks/detail/${record.id}`)}
            />
          </Tooltip>

          <Tooltip title="归档任务">
            <Button
              icon={<StopOutlined />}
              size="small"
              danger
              onClick={() => handleTerminateTask(record.id)}
            />
          </Tooltip>
          <Tooltip title="删除任务">
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
              type="primary"
              onClick={(e) => handleDeleteTask(record.id, e)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];



  // 归档任务
  const handleTerminateTask = (taskId) => {
    message.success(`任务 ${taskId} 已归档`);
    // 更新状态
    setTasks(prevTasks =>
      prevTasks.map(task =>
        task.id === taskId ? { ...task, status: 'terminated' } : task
      )
    );
  };

  // 删除任务
  const handleDeleteTask = (taskId, event) => {
    // 阻止事件冒泡，避免触发卡片的点击事件
    if (event) {
      event.stopPropagation();
    }

    // 确认对话框
    Modal.confirm({
      title: '确认删除任务',
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      content: (
        <div>
          <p>您确定要删除此行动任务吗？</p>
          <p><b>警告：</b>此操作将永久删除以下内容，且<b>不可恢复</b>：</p>
          <ul>
            <li>任务基本信息</li>
            <li><b>所有正在运行的自主行动（将被立即停止并删除）</b></li>
            <li>所有关联的智能体及其变量</li>
            <li>所有环境变量数据</li>
            <li>所有会话历史记录</li>
            <li>所有项目空间文件（共享工作区和智能体工作空间）</li>
          </ul>
        </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 调用API删除任务及其所有关联数据，启用强制清理
          const result = await actionTaskAPI.delete(taskId, true, true);

          // 从列表中移除该任务
          setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));

          // 显示详细的删除结果消息
          if (result.stopped_autonomous_tasks > 0) {
            message.success(`任务已成功删除，同时停止并删除了 ${result.stopped_autonomous_tasks} 个自主行动`);
          } else {
            message.success(`任务已成功删除`);
          }
        } catch (error) {
          console.error('删除任务失败:', error);
          message.error(`删除任务失败: ${error.message || '未知错误'}`);
        }
      }
    });
  };

  // 渲染创建任务的表单
  const renderCreateForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleCreateTask}
      >
        <Form.Item
          name="title"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入任务名称" />
        </Form.Item>

        <Form.Item
          name="action_space_id"
          label="行动空间"
          rules={[{ required: true, message: '请选择行动空间' }]}
        >
          <Select
            placeholder="请选择行动空间"
            onChange={handleActionSpaceChange}
            loading={loadingResources}
          >
            {actionSpaces.map(space => (
              <Select.Option key={space.id} value={space.id}>
                {space.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="rule_set_id"
          label="规则集"
          rules={[{ required: true, message: '请至少选择一个规则集' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择规则集"
            loading={loadingRuleSets}
            style={{ width: '100%' }}
          >
            {ruleSets.map(ruleSet => (
              <Select.Option key={ruleSet.id} value={ruleSet.id}>
                {ruleSet.name}{ruleSet.action_space_name ? ` (${ruleSet.action_space_name})` : ''}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="description"
          label={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>任务描述</span>
              <Button
                type="link"
                size="small"
                icon={<RobotOutlined />}
                loading={assistantGenerating}
                onClick={handleAssistantGenerate}
                style={{
                  padding: '0 8px',
                  height: 'auto',
                  fontSize: '12px'
                }}
              >
                辅助生成
              </Button>
            </div>
          }
        >
          <Input.TextArea
            placeholder="请输入任务描述，或点击右上角的辅助生成按钮自动生成"
            rows={4}
            style={{
              borderColor: assistantGenerating ? '#52c41a' : undefined,
              boxShadow: assistantGenerating ? '0 0 0 2px rgba(82, 196, 26, 0.2)' : undefined
            }}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={createLoading}>
            创建任务
          </Button>
        </Form.Item>
      </Form>
    );
  };

  return (
    <div className="action-task-overview-container">
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>行动任务管理</Title>
            <Text type="secondary">
              创建和管理AI执行的行动任务，支持多空间任务协调和自动化流程执行
            </Text>
          </div>
          <Space>
            <Input
              placeholder="搜索任务"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{
                width: 250,
                height: '42px',
                fontSize: '14px'
              }}
              size="large"
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateModal}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              创建任务
            </Button>
          </Space>
        </div>
      </div>

      <Card
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        {loading ? (
          <div style={{ position: 'relative' }}>
            {/* 加载指示器 - 绝对定位，不影响布局 */}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1000,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Spin size="large" />
              <div style={{ color: '#1677ff', fontSize: '14px' }}>加载任务列表</div>
            </div>

            {/* 页面框架 - 完全透明背景 */}
            <div style={{ opacity: 0.3 }}>
              <Tabs
                defaultActiveKey="all"
                tabBarExtraContent={
                  <Radio.Group
                    value={viewMode}
                    onChange={(e) => setViewMode(e.target.value)}
                    optionType="button"
                    buttonStyle="solid"
                    size="small"
                    style={{
                      fontSize: '14px'
                    }}
                  >
                    <Radio.Button
                      value="card"
                      style={{
                        borderRadius: '6px 0 0 6px',
                        fontSize: '14px'
                      }}
                    >
                      <AppstoreOutlined />
                    </Radio.Button>
                    <Radio.Button
                      value="table"
                      style={{
                        borderRadius: '0 6px 6px 0',
                        fontSize: '14px'
                      }}
                    >
                      <OrderedListOutlined />
                    </Radio.Button>
                  </Radio.Group>
                }
                items={[
                  {
                    key: 'all',
                    label: '全部任务',
                    children: <div style={{ minHeight: '400px' }} />
                  },
                  {
                    key: 'active',
                    label: '进行中',
                    children: <div style={{ minHeight: '400px' }} />
                  },

                  {
                    key: 'completed',
                    label: '已完成',
                    children: <div style={{ minHeight: '400px' }} />
                  }
                ]}
              />
            </div>
          </div>
        ) : (
          <Tabs
            defaultActiveKey="all"
            tabBarExtraContent={
              <Radio.Group
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value)}
                optionType="button"
                buttonStyle="solid"
                size="small"
                style={{
                  fontSize: '14px'
                }}
              >
                <Radio.Button
                  value="card"
                  style={{
                    borderRadius: '6px 0 0 6px',
                    fontSize: '14px'
                  }}
                >
                  <AppstoreOutlined />
                </Radio.Button>
                <Radio.Button
                  value="table"
                  style={{
                    borderRadius: '0 6px 6px 0',
                    fontSize: '14px'
                  }}
                >
                  <OrderedListOutlined />
                </Radio.Button>
              </Radio.Group>
            }
            items={[
            {
              key: 'all',
              label: '全部任务',
              children: viewMode === 'card' ? renderTaskCards() : (
                <Table
                  rowSelection={{
                    type: 'checkbox',
                    onChange: (selectedRowKeys) => setSelectedTasks(selectedRowKeys),
                    selectedRowKeys: selectedTasks,
                  }}
                  columns={columns}
                  dataSource={getFilteredTasks()}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              )
            },
            {
              key: 'active',
              label: '进行中',
              children: viewMode === 'card' ?
                renderTaskCards(getFilteredTasks().filter(t => t.status === 'active')) :
                <Table
                  rowSelection={{
                    type: 'checkbox',
                    onChange: (selectedRowKeys) => setSelectedTasks(selectedRowKeys),
                    selectedRowKeys: selectedTasks,
                  }}
                  columns={columns}
                  dataSource={getFilteredTasks().filter(t => t.status === 'active')}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
            },

            {
              key: 'completed',
              label: '已完成',
              children: viewMode === 'card' ?
                renderTaskCards(getFilteredTasks().filter(t => t.status === 'completed')) :
                <Table
                  rowSelection={{
                    type: 'checkbox',
                    onChange: (selectedRowKeys) => setSelectedTasks(selectedRowKeys),
                    selectedRowKeys: selectedTasks,
                  }}
                  columns={columns}
                  dataSource={getFilteredTasks().filter(t => t.status === 'completed')}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
            }
          ]}
        />
        )}
      </Card>

      {/* 创建任务模态框 */}
      <Modal
        title="创建新行动任务"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        {renderCreateForm()}
      </Modal>
    </div>
  );
};

export default ActionTaskOverview;