import React, { useState, useEffect } from 'react';
import {
  List,
  Card,
  Button,
  Input,
  message,
  Empty,
  Typography,
  Space,
  Tag,
  Row,
  Col,
  Modal,
  Spin,
  Alert
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
import api from '../../../services/api/axios';
import PartitionDetailModal from './PartitionDetailModal';

const { Title, Text } = Typography;
const { Search } = Input;

const PartitionBrowserTab = ({ config, overview, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [partitions, setPartitions] = useState([]);
  const [filteredPartitions, setFilteredPartitions] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [selectedPartition, setSelectedPartition] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 加载分区列表
  const loadPartitions = async () => {
    try {
      setLoading(true);
      const response = await api.get('/memory/partitions');
      const data = response.data;

      if (data.success) {
        setPartitions(data.data);
        setFilteredPartitions(data.data);
      } else {
        message.error(`加载分区列表失败: ${data.message}`);
      }
    } catch (error) {
      console.error('加载分区列表失败:', error);
      message.error('加载分区列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (config?.enabled) {
      loadPartitions();
    }
  }, [config]);

  // 搜索过滤
  useEffect(() => {
    if (!searchText) {
      setFilteredPartitions(partitions);
    } else {
      const filtered = partitions.filter(partition =>
        partition.name.toLowerCase().includes(searchText.toLowerCase()) ||
        partition.description.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredPartitions(filtered);
    }
  }, [searchText, partitions]);

  // 查看分区详情
  const handleViewPartition = (partition) => {
    setSelectedPartition(partition);
    setDetailModalVisible(true);
  };

  // 清空分区
  const handleClearPartition = (partition) => {
    Modal.confirm({
      title: '确认清空分区',
      content: `确定要清空分区 "${partition.name}" 的所有记忆数据吗？此操作不可恢复。`,
      okText: '确认清空',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await api.post(`/memory/partition/${partition.id}/clear`);
          const data = response.data;

          if (data.success) {
            message.success('分区数据清空成功');
            loadPartitions();
            onRefresh && onRefresh();
          } else {
            message.error(`清空失败: ${data.message}`);
          }
        } catch (error) {
          console.error('清空分区失败:', error);
          message.error('清空分区失败');
        }
      },
    });
  };

  // 渲染分区类型标签
  const renderPartitionTypeTag = (type) => {
    const typeConfig = {
      'action_space': { color: 'blue', text: '行动空间' },
      'action_task': { color: 'green', text: '行动任务' },
      'role': { color: 'orange', text: '角色' },
      'agent': { color: 'purple', text: '智能体' }
    };
    
    const config = typeConfig[type] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染分区项
  const renderPartitionItem = (partition) => (
    <List.Item
      key={partition.id}
      actions={[
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewPartition(partition)}
        >
          查看
        </Button>,
        <Button
          type="text"
          icon={<DeleteOutlined />}
          danger
          onClick={() => handleClearPartition(partition)}
        >
          清空
        </Button>
      ]}
    >
      <List.Item.Meta
        avatar={<DatabaseOutlined style={{ fontSize: 24, color: '#1890ff' }} />}
        title={
          <Space>
            {partition.name}
            {renderPartitionTypeTag(partition.type)}
          </Space>
        }
        description={
          <div>
            <Text type="secondary">{partition.description}</Text>
            <br />
            <Space size="large" style={{ marginTop: 8 }}>
              <Text type="secondary">
                <NodeIndexOutlined /> 节点: {partition.node_count || 0}
              </Text>
              <Text type="secondary">
                关系: {partition.edge_count || 0}
              </Text>
            </Space>
          </div>
        }
      />
    </List.Item>
  );

  // 如果图谱增强未启用
  if (!config?.enabled) {
    return (
      <div>
        <Alert
          message="图谱增强未启用"
          description="请先在图谱增强设置中启用图谱增强功能，才能浏览记忆分区。"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div>
      {/* 分区列表 */}
      <Card
        title="分区列表"
        extra={
          <Space>
            <Search
              placeholder="搜索分区..."
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={loadPartitions}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Spin spinning={loading}>
          {filteredPartitions.length > 0 ? (
            <List
              dataSource={filteredPartitions}
              renderItem={renderPartitionItem}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
              }}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchText ? '没有找到匹配的分区' : '暂无分区数据'
              }
            />
          )}
        </Spin>
      </Card>

      {/* 分区详情模态框 */}
      <PartitionDetailModal
        visible={detailModalVisible}
        partition={selectedPartition}
        onClose={() => {
          setDetailModalVisible(false);
          setSelectedPartition(null);
        }}
      />
    </div>
  );
};

export default PartitionBrowserTab;
