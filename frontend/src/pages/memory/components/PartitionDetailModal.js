import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Input,
  Button,
  List,
  Card,
  message,
  Empty,
  Typography,
  Space,
  Spin,
  Descriptions,
  Tag
} from 'antd';
import {
  SearchOutlined,
  NodeIndexOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import api from '../../../services/api/axios';

const { Title, Text } = Typography;
const { Search } = Input;

const PartitionDetailModal = ({ visible, partition, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const [partitionStats, setPartitionStats] = useState(null);
  const [graphData, setGraphData] = useState(null);
  const [searchResults, setSearchResults] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searching, setSearching] = useState(false);

  // 加载分区统计信息
  const loadPartitionStats = async (partitionId) => {
    try {
      const response = await api.get(`/memory/partition/${partitionId}/stats`);
      const data = response.data;

      if (data.success) {
        setPartitionStats(data.data);
      } else {
        console.error('加载分区统计失败:', data.message);
      }
    } catch (error) {
      console.error('加载分区统计失败:', error);
    }
  };

  // 加载分区图谱数据
  const loadGraphData = async (partitionId) => {
    try {
      setLoading(true);
      const response = await api.get(`/memory/partition/${partitionId}/graph?limit=50`);
      const data = response.data;

      if (data.success) {
        setGraphData(data.data);
      } else {
        message.error(`加载图谱数据失败: ${data.message}`);
      }
    } catch (error) {
      console.error('加载图谱数据失败:', error);
      message.error('加载图谱数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索分区内容
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      message.warning('请输入搜索内容');
      return;
    }

    try {
      setSearching(true);
      const response = await api.post(`/memory/partition/${partition.id}/search`, {
        query: searchQuery,
        limit: 20
      });

      const data = response.data;

      if (data.success) {
        setSearchResults(data.data);
        setActiveTab('search');
      } else {
        message.error(`搜索失败: ${data.message}`);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败');
    } finally {
      setSearching(false);
    }
  };

  // 当分区变化时重新加载数据
  useEffect(() => {
    if (visible && partition) {
      setActiveTab('info');
      setSearchResults(null);
      setSearchQuery('');
      loadPartitionStats(partition.id);
      loadGraphData(partition.id);
    }
  }, [visible, partition]);

  // 渲染分区基本信息
  const renderPartitionInfo = () => (
    <div>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="分区ID">
          {partition?.id}
        </Descriptions.Item>
        <Descriptions.Item label="分区名称">
          {partition?.name}
        </Descriptions.Item>
        <Descriptions.Item label="分区类型">
          <Tag color="blue">{partition?.type}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="关联实体">
          {partition?.entity_name}
        </Descriptions.Item>
        <Descriptions.Item label="描述" span={2}>
          {partition?.description || '无描述'}
        </Descriptions.Item>
      </Descriptions>

      {partitionStats && (
        <Card title="统计信息" style={{ marginTop: 16 }}>
          <Descriptions column={3}>
            <Descriptions.Item label="节点数量">
              {partitionStats.node_count || 0}
            </Descriptions.Item>
            <Descriptions.Item label="关系数量">
              {partitionStats.edge_count || 0}
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              {partitionStats.last_updated ? 
                new Date(partitionStats.last_updated).toLocaleString() : 
                '未知'
              }
            </Descriptions.Item>
          </Descriptions>
          {partitionStats.message && (
            <Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
              {partitionStats.message}
            </Text>
          )}
        </Card>
      )}
    </div>
  );

  // 渲染图谱数据
  const renderGraphData = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Text strong>图谱数据</Text>
          <Button 
            size="small" 
            icon={<ReloadOutlined />}
            onClick={() => loadGraphData(partition.id)}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {graphData && graphData.nodes && graphData.nodes.length > 0 ? (
          <div>
            <Card title={`节点 (${graphData.nodes.length})`} size="small" style={{ marginBottom: 16 }}>
              <List
                size="small"
                dataSource={graphData.nodes.slice(0, 10)}
                renderItem={(node) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<NodeIndexOutlined />}
                      title={node.label || node.id}
                      description={
                        <Space>
                          <Tag>{node.type}</Tag>
                          {node.properties && Object.keys(node.properties).length > 0 && (
                            <Text type="secondary">
                              {Object.keys(node.properties).length} 个属性
                            </Text>
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
              {graphData.nodes.length > 10 && (
                <Text type="secondary">
                  还有 {graphData.nodes.length - 10} 个节点...
                </Text>
              )}
            </Card>

            {graphData.edges && graphData.edges.length > 0 && (
              <Card title={`关系 (${graphData.edges.length})`} size="small">
                <List
                  size="small"
                  dataSource={graphData.edges.slice(0, 5)}
                  renderItem={(edge) => (
                    <List.Item>
                      <Text>
                        {edge.source} 
                        <Text type="secondary"> → </Text>
                        {edge.target}
                        <Tag style={{ marginLeft: 8 }}>{edge.relationship}</Tag>
                      </Text>
                    </List.Item>
                  )}
                />
                {graphData.edges.length > 5 && (
                  <Text type="secondary">
                    还有 {graphData.edges.length - 5} 个关系...
                  </Text>
                )}
              </Card>
            )}
          </div>
        ) : (
          <Empty 
            description={graphData?.message || "暂无图谱数据"}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Spin>
    </div>
  );

  // 渲染搜索结果
  const renderSearchResults = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder="搜索分区内容..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onPressEnter={handleSearch}
          />
          <Button 
            type="primary" 
            icon={<SearchOutlined />}
            onClick={handleSearch}
            loading={searching}
          >
            搜索
          </Button>
        </Space.Compact>
      </div>

      {searchResults ? (
        searchResults.results && searchResults.results.length > 0 ? (
          <List
            dataSource={searchResults.results}
            renderItem={(result) => (
              <List.Item>
                <List.Item.Meta
                  title={result.title || '搜索结果'}
                  description={result.content || result.description}
                />
              </List.Item>
            )}
          />
        ) : (
          <Empty 
            description={searchResults.message || "没有找到相关内容"}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )
      ) : (
        <Empty 
          description="请输入关键词进行搜索"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </div>
  );

  return (
    <Modal
      title={`分区详情: ${partition?.name || ''}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'info',
            label: '基本信息',
            children: renderPartitionInfo()
          },
          {
            key: 'graph',
            label: '图谱数据',
            children: renderGraphData()
          },
          {
            key: 'search',
            label: '内容搜索',
            children: renderSearchResults()
          }
        ]}
      />
    </Modal>
  );
};

export default PartitionDetailModal;
