import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  But<PERSON>,
  message,
  Spin,
  Alert,
  Typography
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import api from '../../services/api/axios';
import PartitionSettingsTab from './components/PartitionSettingsTab';
import PartitionBrowserTab from './components/PartitionBrowserTab';

const { Title, Text } = Typography;

const MemoryPartitionPage = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');
  const [partitionConfig, setPartitionConfig] = useState(null);
  const [memoryOverview, setMemoryOverview] = useState(null);

  // 加载分区配置
  const loadPartitionConfig = async () => {
    try {
      setLoading(true);
      const response = await api.get('/memory/partition-config');
      const data = response.data;

      if (data.success) {
        setPartitionConfig(data.data);
      } else {
        message.error(`加载分区配置失败: ${data.message}`);
      }
    } catch (error) {
      console.error('加载分区配置失败:', error);
      message.error('加载分区配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载记忆系统总览
  const loadMemoryOverview = async () => {
    try {
      const response = await api.get('/memory/overview');
      const data = response.data;

      if (data.success) {
        setMemoryOverview(data.data);
      } else {
        console.error('加载记忆系统总览失败:', data.message);
      }
    } catch (error) {
      console.error('加载记忆系统总览失败:', error);
    }
  };

  // 页面初始化
  useEffect(() => {
    loadPartitionConfig();
    loadMemoryOverview();
  }, []);

  // 刷新数据
  const handleRefresh = () => {
    loadPartitionConfig();
    loadMemoryOverview();
  };

  // 渲染页面头部
  const renderHeader = () => (
    <div style={{ marginBottom: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <div>
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>
            记忆管理
          </Title>
          <Text type="secondary">
            管理智能体的记忆系统，包括分区策略配置和记忆数据浏览
          </Text>
        </div>
      </div>
    </div>
  );

  // 渲染状态提示
  const renderStatusAlert = () => {
    if (!partitionConfig) return null;

    if (!partitionConfig.enabled) {
      return (
        <Alert
          message="图谱增强未启用"
          description="请先在图谱增强设置中启用图谱增强功能，然后配置记忆分区策略。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" type="primary">
              前往设置
            </Button>
          }
        />
      );
    }

    return null;
  };

  return (
    <div>
      {renderHeader()}
      {renderStatusAlert()}

      <Spin spinning={loading}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'settings',
              label: (
                <span>
                  <SettingOutlined />
                  分区配置
                </span>
              ),
              children: (
                <PartitionSettingsTab
                  config={partitionConfig}
                  onConfigUpdate={loadPartitionConfig}
                  loading={loading}
                />
              )
            },
            {
              key: 'browser',
              label: (
                <span>
                  <EyeOutlined />
                  分区浏览
                </span>
              ),
              children: (
                <PartitionBrowserTab
                  config={partitionConfig}
                  overview={memoryOverview}
                  onRefresh={handleRefresh}
                />
              )
            }
          ]}
        />
      </Spin>
    </div>
  );
};

export default MemoryPartitionPage;
